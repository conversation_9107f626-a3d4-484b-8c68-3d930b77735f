from flask import request, jsonify, session, flash, redirect, url_for, current_app # Added current_app
from datetime import datetime, timedelta
import google.generativeai as genai
from pinecone import Pinecone
import os
import re
import json
import base64
import urllib.parse
import time
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from models import db, Part, Submission, IncompleteSubmission, ProblemSetSubmission, MarkingPoint, DailyActivity, DailyActiveTime, User, Question
# Import utilities and decorators
from .utils import login_required, update_user_activity, error_logger, app_logger
from semantic_search import search_with_context

# Note: AI clients (Groq, Mistral) might be better initialized in the app factory
# and passed here, or accessed via app.config/current_app.
# For now, assume they are passed to the registration function.

class GradingTimer:
    """Utility class to track timing for grading steps"""
    def __init__(self):
        self.steps = []
        self.start_time = time.time()
        self.current_step_start = None

    def start_step(self, step_name):
        """Start timing a new step"""
        current_time = time.time()
        if self.current_step_start is not None:
            # End the previous step
            self.end_current_step()
        self.current_step_start = current_time
        self.current_step_name = step_name

    def end_current_step(self):
        """End the current step and record its duration"""
        if self.current_step_start is not None:
            duration = time.time() - self.current_step_start
            self.steps.append({
                'name': self.current_step_name,
                'duration_ms': round(duration * 1000, 2),
                'duration_s': round(duration, 3)
            })
            self.current_step_start = None

    def get_summary(self):
        """Get a summary of all timing steps"""
        # End current step if still running
        if self.current_step_start is not None:
            self.end_current_step()

        total_duration = time.time() - self.start_time
        return {
            'total_duration_ms': round(total_duration * 1000, 2),
            'total_duration_s': round(total_duration, 3),
            'steps': self.steps,
            'step_count': len(self.steps)
        }

def register_api_routes(app, db, session, limiter, groq_client, mistral_client, gemini_grading_client): # Pass limiter and AI clients

    def _evaluate_content_rubric(user_answer: str, content_rubric: dict, part_data: Part, gemini_model, app_logger) -> dict:
        """
        Evaluate user answer against content rubric requirements.
        First checks if answer addresses the question, then evaluates specific requirements.
        Returns a dictionary with boolean flags for each content requirement.
        """
        try:
            # First check if the answer addresses the question at all
            logic_check_prompt = f"""
You are an expert educational assessor. First determine if the student's answer logically addresses the question being asked.

QUESTION: {part_data.description}

STUDENT ANSWER: {user_answer}

Does the student's answer attempt to address the question being asked? Consider:
- Is the answer relevant to the question topic?
- Does it show understanding of what is being asked?
- Is it a genuine attempt (not just random text or "I don't know")?

Return ONLY a JSON object with:
{{"addresses_question": true/false, "reason": "brief explanation"}}
"""

            logic_response = gemini_model.generate_content(
                logic_check_prompt,
                generation_config={
                    'temperature': 0.1,
                    'response_mime_type': 'application/json'
                }
            )

            logic_result = json.loads(logic_response.text)
            app_logger.info(f"Logic check completed: {logic_result}")

            # If answer doesn't address the question, return all false
            if not logic_result.get('addresses_question', False):
                app_logger.info("Answer does not address the question - awarding 0 points")
                return {req_id: False for req_id in content_rubric.keys()}

            # Format content requirements for the prompt
            requirements_text = ""
            for req_id, requirement in content_rubric.items():
                requirements_text += f"- Requirement {req_id}: {requirement}\n"

            prompt = f"""
You are an expert educational assessor. Evaluate whether the student's answer meets each specific content requirement.

QUESTION: {part_data.description}

CONTENT REQUIREMENTS:
{requirements_text}

STUDENT ANSWER:
{user_answer}

For each requirement, determine if it is present in the student's answer. Be objective and precise.

Return ONLY a valid JSON dictionary where:
- Each key is the requirement ID (as string)
- Each value is true if the requirement is met, false if not met

Example format:
{{"1": true, "2": false, "3": true}}
"""

            response = gemini_model.generate_content(
                prompt,
                generation_config={
                    'temperature': 0.1,
                    'response_mime_type': 'application/json'
                }
            )

            evaluation_results = json.loads(response.text)
            app_logger.info(f"Content rubric evaluation completed: {evaluation_results}")
            return evaluation_results

        except Exception as e:
            app_logger.exception(f"Error evaluating content rubric: {str(e)}")
            # Return all false if evaluation fails
            return {req_id: False for req_id in content_rubric.keys()}

    def _calculate_score_from_rubric(content_evaluation: dict, scoring_rubric: dict, app_logger) -> tuple:
        """
        Calculate score using bit-mapped scoring rubric based on content evaluation.
        Returns (score, bit_combination)
        """
        try:
            # Calculate bit combination from content evaluation
            bit_combination = 0
            for i, (req_id, is_met) in enumerate(content_evaluation.items()):
                if is_met:
                    bit_combination += (1 << i)  # Set bit i

            # Get score from scoring rubric
            score = scoring_rubric.get(bit_combination, 0)

            app_logger.info(f"Bit combination: {bit_combination}, Score: {score}")
            return score, bit_combination

        except Exception as e:
            app_logger.exception(f"Error calculating score from rubric: {str(e)}")
            return 0, 0

    def _find_study_materials_for_missing_content(content_rubric: dict, content_evaluation: dict, part_data: Part, app_logger) -> dict:
        """
        Get precomputed study materials for missing content requirements.
        Returns a dictionary mapping requirement IDs to study material suggestions.
        """
        study_materials = {}

        try:
            # Check if part has precomputed study materials
            if part_data.study_materials:
                precomputed_materials = json.loads(part_data.study_materials)
                app_logger.info(f"Using precomputed study materials for part {part_data.id}")

                # Return only materials for missing requirements
                for req_id, requirement in content_rubric.items():
                    if not content_evaluation.get(req_id, False):  # If requirement not met
                        if req_id in precomputed_materials:
                            study_materials[req_id] = precomputed_materials[req_id]
                            app_logger.info(f"Found precomputed study materials for requirement {req_id}")
                        else:
                            app_logger.warning(f"No precomputed study materials found for requirement {req_id}")
                            study_materials[req_id] = {
                                'requirement': requirement,
                                'materials': []
                            }
            else:
                # Fallback to live Pinecone search if no precomputed materials
                app_logger.warning(f"No precomputed study materials for part {part_data.id}, falling back to live search")

                for req_id, requirement in content_rubric.items():
                    if not content_evaluation.get(req_id, False):  # If requirement not met
                        # Create search query from the requirement and question context
                        search_query = f"{requirement}"
                        if part_data.question.topic:
                            search_query += f" {part_data.question.topic.name}"
                        if part_data.question.topic and part_data.question.topic.subject:
                            search_query += f" {part_data.question.topic.subject.name}"

                        # Search for relevant materials
                        search_results = search_with_context(search_query, top_k=3)

                        if search_results:
                            study_materials[req_id] = {
                                'requirement': requirement,
                                'materials': search_results
                            }
                            app_logger.info(f"Found {len(search_results)} study materials for requirement {req_id}")
                        else:
                            app_logger.warning(f"No study materials found for requirement {req_id}")
                            study_materials[req_id] = {
                                'requirement': requirement,
                                'materials': []
                            }

        except Exception as e:
            app_logger.exception(f"Error finding study materials: {str(e)}")

        app_logger.info(f"Study materials summary: {len(study_materials)} requirements with materials")
        return study_materials

    def _generate_feedback_comments(user_answer: str, content_rubric: dict, content_evaluation: dict,
                                   study_materials: dict, total_score: int, max_score: int,
                                   gemini_model, app_logger) -> str:
        """
        Generate objective feedback comments with HTML links to study materials.
        """
        try:
            if total_score == max_score:
                return f"<p><strong>Score: {total_score}/{max_score}</strong>. All requirements met.</p>"

            # Format missed requirements with their study material URLs
            missed_requirements_with_links = []
            met_requirements = []

            for req_id, requirement in content_rubric.items():
                if content_evaluation.get(req_id, False):
                    met_requirements.append(requirement)
                else:
                    # Get study materials for this missed requirement
                    materials = study_materials.get(req_id, {}).get('materials', [])

                    # Format requirement with study links
                    requirement_text = f"Requirement {req_id}: {requirement}"
                    if materials:
                        links = []
                        for material in materials[:3]:  # Top 3 materials
                            links.append(f"{material['title']} ({material['chapter']}) - {material['url']}")
                        requirement_text += f"\nStudy materials: {'; '.join(links)}"

                    missed_requirements_with_links.append(requirement_text)

            # Create structured feedback for Gemini
            feedback_data = {
                'score': f"{total_score}/{max_score}",
                'met_count': len(met_requirements),
                'total_count': len(content_rubric),
                'missed_requirements': missed_requirements_with_links
            }

            # Generate HTML feedback using Gemini
            prompt = f"""
Generate objective, professional feedback in HTML format. Use the following data:

SCORE: {feedback_data['score']}
REQUIREMENTS MET: {feedback_data['met_count']}/{feedback_data['total_count']}
MISSED REQUIREMENTS WITH STUDY MATERIALS:
{chr(10).join(missed_requirements_with_links)}

Create HTML feedback that:
1. Shows the score prominently with <strong> tags
2. Lists requirements met vs total
3. For each missed requirement, show the requirement and convert study material URLs to clickable <a> tags
4. Use proper grammar and professional tone
5. Keep it objective and factual
6. Use <p> tags for paragraphs and <ul><li> for lists if needed

Format study material links as: <a href="URL" target="_blank">Title (Chapter)</a>

Return only the HTML content without any markdown formatting or code blocks.
"""

            response = gemini_model.generate_content(
                prompt,
                generation_config={'temperature': 0.2}
            )

            return response.text.strip()

        except Exception as e:
            app_logger.exception(f"Error generating feedback comments: {str(e)}")
            # Fallback comment
            return f"<p><strong>Score: {total_score}/{max_score}</strong>. Review requirements and study materials.</p>"

    def _generate_html_tooltips(content_rubric: dict, content_evaluation: dict, study_materials: dict, gemini_model, app_logger) -> dict:
        """
        Generate HTML tooltips for each requirement with study material links.
        Returns a dictionary mapping requirement IDs to HTML tooltip content.
        """
        try:
            tooltips = {}

            # Process requirements in parallel
            with ThreadPoolExecutor(max_workers=4) as executor:
                # Submit tooltip generation tasks
                tooltip_futures = {}
                for req_id, requirement in content_rubric.items():
                    is_met = content_evaluation.get(req_id, False)
                    materials = study_materials.get(req_id, {}).get('materials', [])

                    future = executor.submit(
                        _generate_single_tooltip,
                        req_id, requirement, is_met, materials, gemini_model, app_logger
                    )
                    tooltip_futures[req_id] = future

                # Collect results
                for req_id, future in tooltip_futures.items():
                    try:
                        tooltips[req_id] = future.result()
                    except Exception as e:
                        app_logger.exception(f"Error generating tooltip for requirement {req_id}: {str(e)}")
                        tooltips[req_id] = f"Requirement {req_id}: {content_rubric[req_id]}"

            return tooltips

        except Exception as e:
            app_logger.exception(f"Error generating HTML tooltips: {str(e)}")
            # Fallback tooltips
            return {
                req_id: f"Requirement {req_id}: {requirement}"
                for req_id, requirement in content_rubric.items()
            }

    def _generate_single_tooltip(req_id: str, requirement: str, is_met: bool, materials: list, gemini_model, app_logger) -> str:
        """Generate a single HTML tooltip for a requirement"""
        try:
            status = "✅ Met" if is_met else "❌ Not Met"

            # Format study materials if available
            materials_html = ""
            if materials and not is_met:
                materials_html = "<br><strong>Study Materials:</strong><br>"
                for material in materials[:2]:  # Top 2 materials
                    materials_html += f'• <a href="{material["url"]}" target="_blank">{material["title"]}</a><br>'

            tooltip_content = f"""
            <div class="tooltip-content">
                <strong>Requirement {req_id}:</strong> {requirement}<br>
                <span class="status">{status}</span>
                {materials_html}
            </div>
            """

            return tooltip_content.strip()

        except Exception as e:
            app_logger.exception(f"Error generating single tooltip for {req_id}: {str(e)}")
            return f"Requirement {req_id}: {requirement}"


    def _process_single_marking_point(mp_data, mp_index, user_answer, part_data, marking_points_data,
                                     app_logger, assigned_border_class):
        """
        Process a single marking point and return the evaluation result.
        This function is designed to be called in parallel for each marking point.
        """
        point_score = 0
        is_correct_mp = False
        is_partial_mp = False
        evidence_mp = None
        error_mp = False

        try:

            # Generate LLM prompt
            prompt = f"""\
                You are an expert examiner evaluating a student's answer against a specific marking point.
                TASK:
                1. Determine if the student's answer demonstrates understanding of the marking point
                2. Classify the answer and provide structured feedback in one of these formats:
                   - If FULLY CORRECT: "Correctly identified <concept>"
                   - If the student attempted to address the concept but was INCORRECT: "Incorrecty explained <concept"
                   - If the student OMITTED the concept entirely: "Omitted <concept>"
                3. For FULLY or PARTIALLY correct answers, identify the exact text from the student's answer that provides evidence

                RESPONSE FORMAT:
                You must respond in one of these four formats ONLY:

                Format 1 - If the marking point is FULLY addressed:
                YES
                EVIDENCE: <exact text from student's answer>

                Format 2 - If the marking point was PARTIALLY addressed
                PARTIAL
                EVIDENCE: <exact text from student's answer>

                Format 3 - If the marking point is NOT addressed
                NO

                IMPORTANT: Do not include any other text, explanations, or formatting in your response.

                MARKING POINT: {mp_data['description']}
                OTHER MARKING POINTS (exclude these from your evaluation): {', '.join([other_mp['description'] for other_mp in marking_points_data if other_mp['id'] != mp_data['id']])}
                STUDENT'S ANSWER: {user_answer}
                """

            # Call LLM
            generation_config = {
                "temperature": 0.3, "top_p": 0.95, "top_k": 40, "max_output_tokens": 4096,
            }
            safety_settings = [
                {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
            ]
            response_text = gemini_grading_client.generate_content(
                prompt,
                generation_config=generation_config,
                safety_settings=safety_settings
            ).text.strip()

            # Process response
            response_upper = response_text.upper()
            is_correct_mp = 'YES' in response_upper and not 'PARTIAL' in response_upper
            is_partial_mp = 'PARTIAL' in response_upper

            app_logger.debug(f"LLM Response for marking point {mp_data['id']}: {response_text}")

            # Extract evidence for correct/partial answers
            if is_correct_mp or is_partial_mp:
                if 'EVIDENCE:' in response_text:
                    evidence_parts = response_text.split('EVIDENCE:', 1)
                    if len(evidence_parts) > 1:
                        evidence_mp = evidence_parts[1].strip()
                elif 'EVIDENCE' in response_text: # Fallback if colon is missing
                    evidence_parts = response_text.split('EVIDENCE', 1)
                    if len(evidence_parts) > 1:
                        evidence_mp = evidence_parts[1].lstrip(':').strip()

            # Calculate score
            if is_correct_mp:
                point_score = mp_data['score']
            elif is_partial_mp:
                point_score = mp_data['score'] * 0.5

        except Exception as e_mp:
            app_logger.exception(f"Error evaluating marking point {mp_data['id']} with LLM: {str(e_mp)}")
            error_mp = True

        # Return result
        return {
            'id': mp_data['id'],
            'description': mp_data['description'],
            'score': mp_data['score'],
            'achieved': is_correct_mp,
            'partial': is_partial_mp,
            'achieved_score': point_score,
            'evidence': evidence_mp,
            'feedback': mp_data['description'],  # Return the marking point description as the correct answer
            'color': assigned_border_class if (is_correct_mp or is_partial_mp) and evidence_mp else None,
            'error': error_mp,
            'mp_index': mp_index  # Include index for ordering results
        }

    # --- IMPORTANT: NEW RUBRIC-BASED GRADING FUNCTION! ---
    def _calculate_score_and_evaluated_points(user_answer: str, part_data: Part, gemini_model, app_logger):
        """
        Calculates the score for a given part using rubric-based grading system.
        This logic is shared between get_git_diff and submit_problemset.
        """
        # Initialize timing tracker
        timer = GradingTimer()
        timer.start_step("Initialization and Setup")

        total_score = 0
        evaluated_points = []

        # Check if part has rubrics generated
        if part_data.content_rubric and part_data.scoring_rubric:
            timer.start_step("Rubric-based Grading")
            try:
                # Parse rubrics from JSON
                content_rubric = json.loads(part_data.content_rubric)
                scoring_rubric = json.loads(part_data.scoring_rubric)

                # Convert scoring rubric keys to integers
                scoring_rubric = {int(k): v for k, v in scoring_rubric.items()}

                timer.start_step("Parallel Content Evaluation")
                # Parallelize content evaluation and study materials search
                with ThreadPoolExecutor(max_workers=3) as executor:
                    # Submit parallel tasks
                    content_eval_future = executor.submit(
                        _evaluate_content_rubric, user_answer, content_rubric, part_data, gemini_model, app_logger
                    )

                    # Wait for content evaluation to complete first
                    content_evaluation = content_eval_future.result()

                    timer.start_step("Parallel Score and Materials")
                    # Now parallelize score calculation and study materials search
                    score_future = executor.submit(
                        _calculate_score_from_rubric, content_evaluation, scoring_rubric, app_logger
                    )
                    materials_future = executor.submit(
                        _find_study_materials_for_missing_content, content_rubric, content_evaluation, part_data, app_logger
                    )

                    # Get results
                    total_score, bit_combination = score_future.result()
                    study_materials = materials_future.result()

                timer.start_step("Parallel Feedback Generation")
                # Generate feedback and HTML tooltips in parallel
                with ThreadPoolExecutor(max_workers=2) as executor:
                    feedback_future = executor.submit(
                        _generate_feedback_comments,
                        user_answer, content_rubric, content_evaluation, study_materials,
                        total_score, part_data.score, gemini_model, app_logger
                    )
                    tooltips_future = executor.submit(
                        _generate_html_tooltips,
                        content_rubric, content_evaluation, study_materials, gemini_model, app_logger
                    )

                    # Get results
                    feedback_comments = feedback_future.result()
                    html_tooltips = tooltips_future.result()

                # Create requirements-based feedback structure (no marking points)
                requirements_feedback = []
                for req_id, requirement in content_rubric.items():
                    is_met = content_evaluation.get(req_id, False)
                    materials = study_materials.get(req_id, {}).get('materials', [])
                    tooltip = html_tooltips.get(req_id, f"Requirement {req_id}: {requirement}")

                    req_feedback = {
                        'id': req_id,
                        'requirement': requirement,
                        'met': is_met,
                        'status': 'met' if is_met else 'not_met',
                        'tooltip': tooltip,
                        'study_materials': materials[:3] if not is_met else []  # Only show materials for unmet requirements
                    }
                    requirements_feedback.append(req_feedback)

                # Create overall feedback structure
                feedback_data = {
                    'type': 'requirements_based',
                    'score': total_score,
                    'max_score': part_data.score,
                    'percentage': round((total_score / part_data.score) * 100) if part_data.score > 0 else 0,
                    'status': 'perfect' if total_score == part_data.score else 'partial' if total_score > 0 else 'needs_work',
                    'feedback_html': feedback_comments,
                    'requirements': requirements_feedback,
                    'summary': {
                        'total_requirements': len(content_rubric),
                        'met_requirements': sum(1 for is_met in content_evaluation.values() if is_met),
                        'missed_requirements': sum(1 for is_met in content_evaluation.values() if not is_met)
                    }
                }

                timer.start_step("Final Compilation")
                timing_summary = timer.get_summary()
                return {'score': total_score, 'feedback_data': feedback_data, 'timing': timing_summary}

            except Exception as e:
                app_logger.exception(f"Error in rubric-based grading: {str(e)}")
                # Fall back to marking points if rubric grading fails
                timer.start_step("Fallback to Marking Points")

        # Fallback: Use existing marking points system if no rubrics or error occurred

        if part_data.input_type == 'mcq':
            timer.start_step("MCQ Processing")
            # For MCQ, the answer is the selected option index (passed as user_answer)
            if not user_answer: # Should be validated before calling this helper
                timer.start_step("MCQ Error - No Answer")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'error': 'No answer provided for MCQ', 'timing': timing_summary}

            try:
                timer.start_step("MCQ Validation")
                selected_option_index = int(user_answer)
                options = part_data.options

                if not options or selected_option_index >= len(options) or selected_option_index < 0:
                    timer.start_step("MCQ Error - Invalid Option")
                    timing_summary = timer.get_summary()
                    return {'score': 0, 'evaluated_points': [], 'error': 'Invalid option selected for MCQ', 'timing': timing_summary}

                timer.start_step("MCQ Scoring")
                selected_option = options[selected_option_index]
                is_correct = selected_option.is_correct # Assuming is_correct is a boolean field
                total_score = part_data.score if is_correct else 0

                timer.start_step("MCQ Feedback Generation")
                # For MCQs, create structured feedback based on correctness
                if is_correct:
                    feedback_text = f"Correctly identified {selected_option.description}"
                else:
                    # Find the correct option for feedback
                    correct_option = next((opt for opt in options if opt.is_correct), None)
                    if correct_option:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}' instead of '{correct_option.description}'"
                    else:
                        feedback_text = f"Incorrectly explained - selected '{selected_option.description}'"

                timer.start_step("MCQ Result Compilation")
                evaluated_points.append({
                    'id': f"mcq_{part_data.id}",
                    'description': f"Selected option: {selected_option.description}",
                    'score': part_data.score,
                    'achieved': is_correct,
                    'partial': False,
                    'achieved_score': total_score,
                    'evidence': str(selected_option_index),
                    'feedback': feedback_text,
                    'color': 'border-green-400' if is_correct else 'border-red-400'
                })

                timing_summary = timer.get_summary()
                return {'score': total_score, 'evaluated_points': evaluated_points, 'timing': timing_summary}

            except (ValueError, TypeError) as e:
                timer.start_step("MCQ Error - Exception")
                app_logger.exception(f"Error processing MCQ answer in helper: {str(e)}")
                timing_summary = timer.get_summary()
                return {'score': 0, 'evaluated_points': [], 'error': 'Invalid MCQ answer format', 'timing': timing_summary}

        # For non-MCQ questions (free response with marking points)
        timer.start_step("Free Response Setup")
        try:
            marking_points_data = [{
                'id': mp.id,
                'description': mp.description,
                'score': mp.score
            } for mp in part_data.marking_points]

            highlight_border_classes = [
                'border-yellow-400', 'border-blue-400', 'border-green-400',
                'border-pink-400', 'border-purple-400', 'border-indigo-400',
                'border-teal-400', 'border-orange-400', 'border-lime-400',
                'border-cyan-400'
            ]
            color_index = 0

            timer.start_step(f"Processing {len(marking_points_data)} Marking Points in Parallel")
            parallel_start_time = time.time()

            # Process all marking points in parallel
            with ThreadPoolExecutor(max_workers=min(len(marking_points_data), 10)) as executor:
                # Prepare tasks for parallel execution
                future_to_mp = {}
                for mp_index, mp_data in enumerate(marking_points_data):
                    assigned_border_class = highlight_border_classes[color_index % len(highlight_border_classes)]
                    color_index += 1

                    # Submit task to thread pool
                    future = executor.submit(
                        _process_single_marking_point,
                        mp_data, mp_index, user_answer, part_data, marking_points_data,
                        app_logger, assigned_border_class
                    )
                    future_to_mp[future] = mp_index

                # Collect results as they complete
                results = [None] * len(marking_points_data)  # Pre-allocate list to maintain order
                completed_count = 0
                for future in as_completed(future_to_mp):
                    try:
                        result = future.result()
                        mp_index = result['mp_index']
                        results[mp_index] = result
                        completed_count += 1
                        app_logger.debug(f"Completed marking point {mp_index + 1}/{len(marking_points_data)} in parallel")
                    except Exception as e:
                        mp_index = future_to_mp[future]
                        app_logger.exception(f"Error in parallel processing of marking point {mp_index}: {str(e)}")
                        # Create error result
                        mp_data = marking_points_data[mp_index]
                        results[mp_index] = {
                            'id': mp_data['id'],
                            'description': mp_data['description'],
                            'score': mp_data['score'],
                            'achieved': False,
                            'partial': False,
                            'achieved_score': 0,
                            'evidence': None,
                            'feedback': mp_data['description'],
                            'color': None,
                            'error': True,
                            'mp_index': mp_index
                        }
                        completed_count += 1

            parallel_end_time = time.time()
            parallel_duration = parallel_end_time - parallel_start_time

            # Add parallel processing timing information
            timer.steps.append({
                'name': f"Parallel Processing of {len(marking_points_data)} Marking Points",
                'duration_ms': round(parallel_duration * 1000, 2),
                'duration_s': round(parallel_duration, 3),
                'details': f"Processed {len(marking_points_data)} marking points concurrently"
            })

            timer.start_step("Result Compilation and Score Calculation")
            # Calculate total score and prepare evaluated points
            total_score = 0
            evaluated_points = []
            for result in results:
                if result:  # Ensure result is not None
                    total_score += result['achieved_score']
                    # Remove mp_index from result before adding to evaluated_points
                    result_copy = result.copy()
                    result_copy.pop('mp_index', None)
                    evaluated_points.append(result_copy)

            timer.start_step("Final Score Compilation")
            timing_summary = timer.get_summary()
            return {'score': total_score, 'evaluated_points': evaluated_points, 'timing': timing_summary}

        except Exception as e:
            timer.start_step("Error Handling - Free Response")
            app_logger.exception(f"Error processing free-response answer in helper: {str(e)}")
            timing_summary = timer.get_summary()
            # Return 0 score and empty points if a major error occurs in this block
            return {'score': 0, 'evaluated_points': [], 'error': 'Error processing marking points', 'timing': timing_summary}
    # --- END: Helper function for grading ---

    # --- START: New Supabase Auth Confirmation Route ---
    @app.route('/api/auth/confirm', methods=['GET'])
    def confirm_auth():
        """
        Handles email confirmation links (e.g., password reset, email change) using Supabase PKCE flow.
        Verifies the token_hash and type, or exchanges code for session, and redirects.
        """
        # Check for code parameter (new PKCE flow)
        code = request.args.get('code')
        if code:
            app_logger.info(f"Auth confirmation request received with code parameter")
            next_url = request.args.get('next', url_for('login'))  # Default redirect to login

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                # Exchange the code for a session
                app_logger.info(f"Attempting to exchange code for session...")
                exchange_response = supabase.auth.exchange_code_for_session({
                    "auth_code": code
                })
                app_logger.info(f"Code exchange response: {exchange_response}")

                # Check if exchange was successful
                if exchange_response and exchange_response.user:
                    supabase_email = exchange_response.user.email
                    app_logger.info(f"Successfully exchanged code for session. User email: {supabase_email}")

                    # Store the email in session
                    session['supabase_email'] = supabase_email
                    session['auth_timestamp'] = datetime.now().timestamp()
                    session.permanent = True
                    session.modified = True

                    # Validate the next_url to prevent open redirect vulnerabilities
                    if next_url and next_url.startswith('/'):
                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(next_url)
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided: {next_url}. Redirecting to login.")
                        flash('Verification successful! Please log in.', 'success')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Code exchange failed. Response: {exchange_response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                error_logger.exception(f"Error during code exchange: {str(e)}")
                flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # Legacy flow with token_hash
        token_hash = request.args.get('token_hash')
        auth_type = request.args.get('type')
        next_url = request.args.get('next', url_for('login')) # Default redirect to login

        if token_hash and auth_type:
            app_logger.info(f"Auth confirmation request received. Type: {auth_type}, Token Hash: {token_hash[:5]}..., Next: {next_url}")

            try:
                # Access the Supabase client
                from app import supabase

                if not supabase:
                    error_logger.critical("Supabase client not available in confirm_auth route.")
                    flash('Authentication system configuration error.', 'error')
                    return redirect(url_for('login'))

                app_logger.info(f"Attempting Supabase verify_otp with type: {auth_type}, token_hash: {token_hash[:5]}...")
                # Verify the OTP (which includes password reset tokens)
                # Get the email from the query parameters if available
                email = request.args.get('email')

                # Use the correct schema for verifying OTP
                verify_data = {
                    "token_hash": token_hash,
                    "type": auth_type
                }

                # Add email to the verification data if available
                if email:
                    verify_data["email"] = email
                    app_logger.info(f"Including email in OTP verification: {email}")
                else:
                    app_logger.info(f"Email not found in query parameters, proceeding without it")

                response = supabase.auth.verify_otp(verify_data)
                app_logger.info(f"Supabase verify_otp response: {response}")

                # Check if verification was successful (user data should be present)
                if response and response.user:
                    supabase_email = response.user.email
                    app_logger.info(f"Supabase user verified: {supabase_email}")

                    # Validate the next_url to prevent open redirect vulnerabilities
                    # Allow only relative paths starting with '/'
                    if next_url and next_url.startswith('/'):
                        # Store the Supabase email and token_hash in the session for use in the reset_password route
                        session['supabase_email'] = supabase_email
                        session['token_hash'] = token_hash
                        session['auth_timestamp'] = datetime.now().timestamp()  # Add timestamp for session freshness check
                        session.permanent = True  # Make session persistent
                        session.modified = True  # Mark session as modified to ensure it's saved

                        # Add the email and token to the redirect URL as query parameters as a fallback
                        # URL encode the parameters to ensure they're properly formatted
                        encoded_email = urllib.parse.quote(supabase_email)
                        encoded_token = urllib.parse.quote(token_hash)
                        redirect_url = f"{next_url}?email={encoded_email}&token_hash={encoded_token}"
                        app_logger.info(f"Adding email and token to redirect URL as fallback: {redirect_url}")

                        # Flash success message
                        flash('Verification successful! Please set your new password.', 'success')
                        return redirect(redirect_url) # Redirect to the URL with query parameters as fallback
                    else:
                        # Invalid 'next' URL provided
                        error_logger.warning(f"Invalid 'next' URL provided in confirmation link: {next_url}. Redirecting verified user {supabase_email} to login.")
                        flash('Verification link error. Redirecting to login.', 'warning')
                        return redirect(url_for('login'))
                else:
                    error_logger.error(f"Supabase verify_otp failed for type {auth_type}. Response: {response}")
                    flash('Verification failed. The link may be invalid or expired.', 'error')
                    return redirect(url_for('login'))

            except Exception as e:
                # Catch potential exceptions from Supabase client or other issues
                error_logger.exception(f"Error during auth confirmation ({auth_type}): {str(e)}")
                # Check for specific Supabase errors if possible (e.g., invalid token)
                if "invalid token" in str(e).lower():
                    flash('Verification failed: Invalid or expired link.', 'error')
                else:
                    flash('An unexpected error occurred during verification.', 'error')
                return redirect(url_for('login'))

        # If we get here, neither code nor token_hash+type were provided
        error_logger.warning("Auth confirmation missing required parameters (code or token_hash+type).")
        flash('Invalid or incomplete confirmation link.', 'error')
        return redirect(url_for('login'))
    # --- END: New Supabase Auth Confirmation Route ---


    # --- Other API Endpoints ---

    @app.route("/check_answer/<int:question_id>/<int:part_id>", methods=['POST'])
    @limiter.limit("30/minute") # Apply rate limit
    @login_required # Require login to check answers
    def check_answer(question_id, part_id):
        if 'user_id' not in session:
            error_logger.warning("Unauthorized attempt to submit answer")
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Handle both text and image inputs
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')
        confidence_level = request.form.get('confidence_level', 'Medium')

        if not user_answer and not image_file:
            error_logger.info(f"Empty answer submitted - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer or upload an image'
            }), 400

        try:
            # Process image if provided
            if image_file:
                # Validate file type
                if not image_file.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid file type. Please upload a PNG, JPG, or JPEG image.'
                    }), 400

                # Encode the image to base64
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro')

                # Create multimodal prompt
                prompt_parts = [
                    "Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
                app_logger.info(f"Image received for OCR - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")

            # Placeholder for score and feedback as 'grader' module is removed.
            # Grading is now handled by get_git_diff route.
            # This route will now primarily log the submission attempt.
            current_score = 0
            feedback_to_user = "Your answer has been submitted. Detailed feedback will be available via the standard grading process."

            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,
                score=current_score
            )
            db.session.add(submission)
            db.session.commit()

            # Trigger feed generation for the user
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(session['user_id'])
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {session['user_id']}: {e}")

            app_logger.info(
                f"Answer submitted - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, "
                f"Score: {submission.score}/{part_data.score}"
            )

            return jsonify({
                'status': 'success',
                'feedback': feedback_to_user,
                'score': submission.score,
                'submission_id': submission.id,
                'max_score': part_data.score
            })

        except Exception as e:
            db.session.rollback()
            error_logger.exception(
                f"Error processing submission - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500



    @app.route('/get_git_diff/<int:question_id>/<int:part_id>', methods=['POST'])
    @login_required
    @limiter.limit("30/minute")
    def get_git_diff(question_id, part_id):
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to submit answers'
            }), 401

        update_user_activity(session['user_id'])
        part_data = Part.query.get_or_404(part_id)

        # Handle both text and image submissions
        user_answer = request.form.get('answer', '').strip()
        image_file = request.files.get('image')

        # If an image was submitted, process it
        if image_file and image_file.filename:
            try:
                # Convert image to base64 for API processing
                image_data = image_file.read()
                base64_image = base64.b64encode(image_data).decode('utf-8')

                # Initialize Gemini vision model
                gemini_vision_model = genai.GenerativeModel('gemini-2.5-pro-preview-05-06')

                # Create multimodal prompt
                prompt_parts = [
                    "You are a LaTeX expert. Convert this mathematical image to LaTeX format. Return ONLY the LaTeX code without any additional text, comments, or formatting. Include ALL mathematical content from the image.",
                    {"mime_type": "image/jpeg", "data": base64_image}
                ]

                # Generate content
                response = gemini_vision_model.generate_content(prompt_parts)
                user_answer = response.text
            except Exception as e:
                error_logger.exception(f"Error processing image submission: {str(e)}")
                return jsonify({
                    'status': 'error',
                    'message': 'Error processing image submission'
                }), 500

        if not user_answer:
            return jsonify({
                'status': 'error',
                'message': 'Please provide an answer'
            }), 400

        # Use the new helper function to calculate score and get feedback data
        grading_details = _calculate_score_and_evaluated_points(user_answer, part_data, gemini_grading_client, app_logger)
        total_score = grading_details['score']
        feedback_data = grading_details.get('feedback_data')
        evaluated_points = grading_details.get('evaluated_points', [])

        if 'error' in grading_details and part_data.input_type == 'mcq': # Handle MCQ specific errors from helper
            return jsonify({'status': 'error', 'message': grading_details['error']}), 400

        # If it's an MCQ, the helper already determined the score and basic feedback.
        # We can return a simplified response for MCQs directly after calling the helper.
        if part_data.input_type == 'mcq':
            # Create submission record
            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer,  # user_answer is the selected_option_index for MCQ
                score=total_score
            )
            db.session.add(submission)
            db.session.commit()

            is_correct_mcq = total_score > 0
            return jsonify({
                'status': 'success',
                'score': total_score,
                'max_score': part_data.score,
                'is_correct': is_correct_mcq,
                'feedback': 'Your answer is correct.' if is_correct_mcq else 'Your answer is incorrect.',
                'marking_points': evaluated_points, # Contains the single MCQ point
                'timing': grading_details.get('timing', {})  # Include timing information
            })

        # For non-MCQ, proceed with highlighting and detailed response
        try:
            # Prepare data for highlighting with feedback information
            import html # Ensure html module is imported
            highlight_data = []
            for point in evaluated_points:
                if (point.get('achieved') or point.get('partial')) and point.get('evidence') and point.get('color'):
                    # Use a dashed border for partial answers, solid for full credit
                    border_style = "border-b-2 " if point.get('achieved') else "border-b-2 border-dashed "
                    highlight_data.append({
                        'evidence': point['evidence'],
                        'color_class': border_style + point['color'], # e.g., 'border-yellow-400'
                        'feedback': point.get('feedback', ''), # Return the correct answer as feedback
                        'description': point.get('description', ''), # Include marking point description
                        'achieved': point.get('achieved', False),
                        'partial': point.get('partial', False),
                        'score': point.get('achieved_score', 0)
                    })

            highlighted_answer = html.escape(user_answer) # Default to escaped original answer
            if highlight_data:
                # Create the prompt for highlighting with feedback information
                highlight_prompt = r"""\
                You are an HTML expert. Given a piece of text and a list of evidence snippets with associated Tailwind CSS border classes and feedback, modify the original text by wrapping EACH evidence snippet EXACTLY as it appears in the text with a span tag that includes both highlighting and tooltip feedback.

                **Instructions:**
                1. Find the occurrences of the evidence snippets within the original text. Do NOT modify the user answer (including changing the order of the answer), just highlight the relevant parts.
                2. Wrap each found evidence snippet with a span tag that includes:
                   - The color class for visual highlighting
                   - A title attribute containing the structured feedback for tooltips
                   - Format: <span class="{{color_class}}" title="{{feedback}}">evidence text</span>
                3. If points overlap, you need to deconflict them in the way that makes the most sense.
                4. Output ONLY the final, complete HTML string. Do not include any introductory text, explanations, or markdown formatting.
                5. Ensure ALL highlighted data is included and inside the appropriately colored spans with feedback tooltips.
                6. Do NOT write stuff like Note: xxx at the end of your output if there are ambiguities.
                7. The order of the marking points in the user answer may be jumbled up. However, you should still give credit and underline parts even if they aren't in order.
                8. Make sure the title attribute is properly escaped for HTML (replace quotes with &quot; if needed).
                9. Write your final result as: FINAL RESULT: <your_html_output>
                """
                other=f"""**User answer:**
                {user_answer}

                **Highlight Data (Evidence, CSS Class, and Feedback):**
                {json.dumps(highlight_data, indent=2)}

                **Example format for each highlighted section: (Note there may be more than one, you must include all)**
                <span class="border-b-2 border-yellow-400" title="Correctly identified kinetic energy formula">KE = 1/2mv²</span>

                **Final HTML Output:**
                """
                highlight_prompt = highlight_prompt + other
                try:
                    # Use Gemini 2.5 Pro for highlighting with strict parameters
                    gemini_pro_model = genai.GenerativeModel('gemini-2.5-flash')

                    generation_config = {
                        "temperature": 0.3,  # Use deterministic output
                        "top_p": 0.95,
                        "top_k": 40,
                    }

                    response = gemini_pro_model.generate_content(
                        highlight_prompt,
                        generation_config=generation_config,
                    )

                    # Check if response was blocked by safety filters
                    if response.candidates and response.candidates[0].finish_reason == 2:  # SAFETY
                        app_logger.warning("Gemini highlighting response blocked by safety filters")
                        highlighted_answer = html.escape(user_answer)
                    elif not response.text:
                        app_logger.warning("Gemini highlighting response has no text content")
                        highlighted_answer = html.escape(user_answer)
                    else:
                        highlighted_answer_raw = response.text.strip()
                        app_logger.info(f"Raw Gemini highlighting response: {highlighted_answer_raw}")

                        if 'FINAL RESULT: ' in highlighted_answer_raw:
                            highlighted_answer_raw = highlighted_answer_raw.split('FINAL RESULT: ')[1]

                            # Basic validation (optional but recommended)
                            if '<span' in highlighted_answer_raw and '</span>' in highlighted_answer_raw:
                                highlighted_answer = highlighted_answer_raw
                            else:
                                # Log a warning if the response doesn't look like HTML
                                app_logger.warning(f"Gemini highlighting response did not seem to contain valid spans: {highlighted_answer_raw}")
                                # Fallback to the default escaped answer
                                highlighted_answer = html.escape(user_answer)
                        else:
                            app_logger.warning("Gemini response missing 'FINAL RESULT: ' marker")
                            highlighted_answer = html.escape(user_answer)

                except Exception as e:
                    error_logger.exception(f"Error calling Gemini API for highlighting: {str(e)}")
                    # Fallback to the default escaped answer in case of API error
                    highlighted_answer = html.escape(user_answer)
            # --- END NEW CODE ---

            # Create a new submission record in the database
            submission = Submission(
                user_id=session['user_id'],
                question_id=question_id,
                part_id=part_id,
                answer=user_answer, # Store original answer
                score=total_score
            )

            # Add and commit to the database
            db.session.add(submission)
            db.session.commit()

            # Trigger feed generation for the user
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(session['user_id'])
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {session['user_id']}: {e}")

            # Log the highlighting data and result for debugging
            app_logger.info(f"Highlighting data: {json.dumps(highlight_data)}")
            app_logger.info(f"Highlighted answer: {highlighted_answer}")
            # Check if we have new requirements-based feedback or old marking points
            if feedback_data:
                return jsonify({
                    'status': 'success',
                    'feedback_data': feedback_data,
                    'score': total_score,
                    'max_score': part_data.score,
                    'answer': highlighted_answer,
                    'timing': grading_details.get('timing', {})
                })
            else:
                # Fallback to old marking points format
                return jsonify({
                    'status': 'success',
                    'marking_points': evaluated_points,
                    'score': total_score,
                    'max_score': part_data.score,
                    'answer': highlighted_answer,
                    'timing': grading_details.get('timing', {})
                })

        except Exception as e:
            error_logger.exception(
                f"Error processing answer - User ID: {session['user_id']}, "
                f"Question ID: {question_id}, Part ID: {part_id}, Error: {str(e)}"
            )
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while processing your answer'
            }), 500


    @app.route('/auto_save', methods=['POST'])
    @login_required
    def auto_save():
        """Saves incomplete progress for a problem set part."""
        user_id = session['user_id']
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        problemset_id = data.get('problemset_id')
        question_id = data.get('question_id')
        part_id = data.get('part_id')
        answer = data.get('answer') # Can be None or empty

        if not all([problemset_id, question_id, part_id]):
            return jsonify({'status': 'error', 'message': 'Missing required data (problemset, question, part IDs).'}), 400

        try:
            # Get or create incomplete submission record with retry logic built into the model method
            submission = IncompleteSubmission.get_or_create(
                problemset_id=problemset_id,
                user_id=user_id,
                question_id=question_id,
                part_id=part_id
            )

            # Update answer and timestamp
            from sqlalchemy.exc import OperationalError
            import time

            max_retries = 3
            retry_delay = 0.1  # seconds

            for attempt in range(max_retries):
                try:
                    submission.answer = answer # Allow None or empty string
                    submission.last_updated = datetime.now()
                    db.session.commit()
                    # app_logger.debug(f"Autosaved progress for User: {user_id}, PS: {problemset_id}, Q: {question_id}, P: {part_id}")
                    return jsonify({'status': 'success'})

                except OperationalError as e:
                    # Handle database locks specifically
                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        db.session.rollback()
                        time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                        continue
                    else:
                        db.session.rollback()
                        raise

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error during auto_save for User {user_id}, PS {problemset_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Failed to save progress.'}), 500


    @app.route('/get_saved_progress/<int:problemset_id>')
    @login_required
    def get_saved_progress(problemset_id):
        """Retrieves saved incomplete progress for a problem set."""
        user_id = session['user_id']
        try:
            submissions = IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=user_id
            ).all()

            # Format data for the frontend
            progress_data = [{
                'question_id': sub.question_id,
                'part_id': sub.part_id,
                'answer': sub.answer if sub.answer is not None else '' # Ensure frontend gets string
            } for sub in submissions]

            return jsonify({
                'status': 'success',
                'submissions': progress_data
            })
        except Exception as e:
            error_logger.exception(f"Error fetching saved progress for User {user_id}, PS {problemset_id}: {e}")
            # Return 200 OK but with error status for AJAX handling
            return jsonify({'status': 'error', 'message': 'Could not retrieve saved progress.'}), 200


    @app.route('/submit_problemset', methods=['POST'])
    @limiter.limit("10/minute") # Limit submission frequency
    @login_required
    def submit_problemset():
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        data = request.get_json()
        problemset_id = data.get('problemset_id')
        submissions = data.get('submissions', [])  # List of {question_id, part_id, answer}

        if not problemset_id:
            return jsonify({'status': 'error', 'message': 'Missing problemset_id'}), 400

        # Initialize the Gemini model once for all parts in this problemset submission

        try:
            # Create a new problemset submission
            problemset_submission = ProblemSetSubmission(
                problemset_id=problemset_id,
                user_id=session['user_id'],
                status='completed',
                submitted_at=datetime.now()
            )
            db.session.add(problemset_submission)
            db.session.flush()  # Get the ID of the new submission

            # Process each answer
            submission_results = []
            for submission_data in submissions:
                question_id = submission_data.get('question_id')
                part_id = submission_data.get('part_id')
                answer = submission_data.get('answer', '').strip()

                if not all([question_id, part_id]):
                    continue

                # Get the part to check its answer and score
                part = Part.query.get(part_id)
                if not part:
                    continue

                # Use the helper function to calculate the score for this part
                # Note: submit_problemset doesn't have Pinecone index, so pass None
                grading_details = _calculate_score_and_evaluated_points(answer, part, gemini_grading_client, app_logger)
                current_score = grading_details['score']
                # We might not need detailed evaluated_points here unless we plan to store them
                # For now, feedback_message can be generic or based on score.
                feedback_message = "Answer submitted and graded." # More accurate message

                # Create the submission
                submission = Submission(
                    user_id=session['user_id'],
                    question_id=question_id,
                    part_id=part_id,
                    answer=answer,
                    score=current_score
                )
                db.session.add(submission)
                problemset_submission.question_submissions.append(submission)

                # Add submission result with feedback
                submission_results.append({
                    'question_id': question_id,
                    'part_id': part_id,
                    'score': current_score,
                    'max_score': part.score,
                    'feedback': feedback_message
                })

            # Calculate scores
            problemset_submission.calculate_scores()

            # Delete incomplete submissions for this problemset
            IncompleteSubmission.query.filter_by(
                problemset_id=problemset_id,
                user_id=session['user_id']
            ).delete()

            db.session.commit()

            # Trigger feed generation for the user after problemset submission
            try:
                from routes.feed_utils import trigger_feed_generation_for_user
                trigger_feed_generation_for_user(session['user_id'])
            except Exception as e:
                app_logger.warning(f"Failed to generate feed posts for user {session['user_id']}: {e}")

            return jsonify({
                'status': 'success',
                'message': 'Problem set submitted successfully',
                'submission_id': problemset_submission.id,
                'submissions': submission_results
            })

        except Exception as e:
            db.session.rollback()
            print(f"Error submitting problem set: {str(e)}")
            return jsonify({'status': 'error', 'message': str(e)}), 500



    # --- Marking Point AJAX Endpoints ---

    @app.route('/extract_marking_points/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def extract_marking_points(part_id):
        """Extract marking points from a question part using an API"""
        part = Part.query.get_or_404(part_id)

        try:
            # Here you would call your API to extract marking points
            # This is a placeholder for the API call
            # api_response = your_api_call(part.description, part.answer)

            # For now, we'll create a sample response
            api_response = {
                'marking_points': [
                    {'description': 'Correct formula', 'score': 2.0},
                    {'description': 'Correct substitution', 'score': 1.0},
                    {'description': 'Correct final answer', 'score': 1.0}
                ]
            }

            # Clear existing auto-generated marking points
            MarkingPoint.query.filter_by(
                part_id=part_id,
                is_auto_generated=True
            ).delete()

            # Add new marking points
            for i, point in enumerate(api_response['marking_points']):
                marking_point = MarkingPoint(
                    part_id=part_id,
                    description=point['description'],
                    score=point['score'],
                    order=i,
                    is_auto_generated=True
                )
                db.session.add(marking_point)

            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking points extracted successfully',
                'marking_points': [{
                    'id': mp.id,
                    'description': mp.description,
                    'score': mp.score,
                    'order': mp.order
                } for mp in part.marking_points]
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 500


    @app.route('/update_marking_point/<int:marking_point_id>', methods=['PUT'])
    @login_required # Should be admin_required?
    def update_marking_point(marking_point_id):
        """Updates a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        data = request.get_json()

        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request format.'}), 400

        try:
            updated = False
            if 'description' in data:
                new_desc = data['description'].strip()
                if new_desc: # Don't allow empty description
                     marking_point.description = new_desc
                     updated = True
                else:
                     return jsonify({'status': 'error', 'message': 'Description cannot be empty.'}), 400
            if 'score' in data:
                try:
                    new_score = float(data['score'])
                    if new_score < 0: # Allow 0 score? Let's prevent negative for now.
                         return jsonify({'status': 'error', 'message': 'Score cannot be negative.'}), 400
                    marking_point.score = new_score
                    updated = True
                except (ValueError, TypeError):
                     return jsonify({'status': 'error', 'message': 'Invalid score format.'}), 400
            if 'order' in data:
                 try:
                     marking_point.order = int(data['order'])
                     updated = True
                 except (ValueError, TypeError):
                      return jsonify({'status': 'error', 'message': 'Invalid order format.'}), 400

            if updated:
                # marking_point.validate() # Call validation if defined in model
                marking_point.is_auto_generated = False # Manual edit overrides auto-gen flag
                db.session.commit()
                app_logger.info(f"Updated marking point {marking_point_id}")
                return jsonify({
                    'status': 'success',
                    'message': 'Marking point updated.',
                    'marking_point': { # Return updated data
                        'id': marking_point.id,
                        'description': marking_point.description,
                        'score': marking_point.score,
                        'order': marking_point.order,
                        'is_auto_generated': marking_point.is_auto_generated
                    }
                })
            else:
                 return jsonify({'status': 'info', 'message': 'No changes detected.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error updating marking point.'}), 500


    @app.route('/delete_marking_point/<int:marking_point_id>', methods=['DELETE'])
    @login_required # Should be admin_required?
    def delete_marking_point(marking_point_id):
        """Deletes a specific marking point."""
        # Add admin check if necessary

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id # Get part ID for logging

        try:
            db.session.delete(marking_point)
            db.session.commit()
            app_logger.info(f"Deleted marking point {marking_point_id} from Part {part_id}")
            return jsonify({'status': 'success', 'message': 'Marking point deleted.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error deleting marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error deleting marking point.'}), 500


    @app.route('/add_marking_point/<int:part_id>', methods=['POST'])
    @login_required # Should be admin_required?
    def add_marking_point(part_id):
        try:
            data = request.get_json()
            part = Part.query.get_or_404(part_id)

            # Create new marking point
            marking_point = MarkingPoint(
                part_id=part_id,
                description=data.get('description', 'New marking point'),
                score=float(data.get('score', 1.0)),
                order=int(data.get('order', 0)),
                is_auto_generated=False
            )

            # Validate the marking point
            marking_point.validate()

            # Add to database
            db.session.add(marking_point)
            db.session.commit()

            return jsonify({
                'status': 'success',
                'message': 'Marking point added successfully',
                'marking_point': {
                    'id': marking_point.id,
                    'description': marking_point.description,
                    'score': marking_point.score,
                    'order': marking_point.order
                }
            })
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'status': 'error',
                'message': str(e)
            }), 400



    @app.route('/move_marking_point/<int:marking_point_id>/<direction>', methods=['POST'])
    @login_required # Should be admin_required?
    def move_marking_point(marking_point_id, direction):
        """Moves a marking point up or down in order within its part."""
        # Add admin check if necessary

        if direction not in ['up', 'down']:
            return jsonify({'status': 'error', 'message': 'Invalid direction.'}), 400

        marking_point = MarkingPoint.query.get_or_404(marking_point_id)
        part_id = marking_point.part_id

        try:
            # Get all marking points for this part, ordered correctly
            siblings = MarkingPoint.query.filter_by(part_id=part_id)\
                                      .order_by(MarkingPoint.order).all()

            try:
                current_index = siblings.index(marking_point)
            except ValueError:
                 # Should not happen if MP exists and belongs to the part
                 error_logger.error(f"Marking point {marking_point_id} not found in siblings list for Part {part_id}")
                 return jsonify({'status': 'error', 'message': 'Marking point consistency error.'}), 500

            if direction == 'up' and current_index > 0:
                # Swap order with the previous sibling
                prev_sibling = siblings[current_index - 1]
                marking_point.order, prev_sibling.order = prev_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} up")
                return jsonify({'status': 'success', 'message': 'Moved up.'})
            elif direction == 'down' and current_index < len(siblings) - 1:
                # Swap order with the next sibling
                next_sibling = siblings[current_index + 1]
                marking_point.order, next_sibling.order = next_sibling.order, marking_point.order
                db.session.commit()
                app_logger.info(f"Moved marking point {marking_point_id} down")
                return jsonify({'status': 'success', 'message': 'Moved down.'})
            else:
                # Cannot move further in this direction
                return jsonify({'status': 'info', 'message': 'Cannot move further.'})

        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error moving marking point {marking_point_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Error moving marking point.'}), 500

    # --- Other API Endpoints ---

    @app.route("/explain_answer/<int:question_id>/<int:part_id>")
    @login_required
    @limiter.limit("10/minute")
    def explain_answer(question_id, part_id):
        """
        Explains the answer for a specific question part using Gemini LLM.
        Returns a streaming response with the explanation.
        """
        if 'user_id' not in session:
            return jsonify({
                'status': 'error',
                'message': 'Please login to get answer explanations'
            }), 401

        update_user_activity(session['user_id'])

        try:
            # Get the part data
            part_data = Part.query.get_or_404(part_id)
            question_data = Question.query.get_or_404(question_id)

            # Initialize Pinecone for context retrieval
            pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
            index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))

            # Query Pinecone for relevant context
            context_text = ""
            try:
                # Create query text from question and topic context
                query_text = f"{part_data.description}"
                if part_data.answer:
                    query_text += f" {part_data.answer}"
                if part_data.question.topic:
                    query_text += f" {part_data.question.topic.name}"
                if part_data.question.topic.subject:
                    query_text += f" {part_data.question.topic.subject.name}"

                # Query Pinecone index for relevant context
                query_payload = {
                    "inputs": {
                        "text": query_text
                    },
                    "top_k": 5  # Get more context for explanations
                }

                query_response = index.search(
                    namespace="__default__",
                    query=query_payload
                )

                for item in query_response['result']['hits']:
                    context_text += f"{item['fields']['title']}: {item['fields']['content']}\n"

            except Exception as e_pinecone:
                app_logger.warning(f"Pinecone query failed for explanation {part_id}: {str(e_pinecone)}")
                context_text = ""  # Continue without context if Pinecone fails

            # Construct the prompt
            if part_data.input_type == 'mcq':
                # For MCQ, include the options and correct answer
                options_text = ""
                correct_option = None

                for i, option in enumerate(part_data.options):
                    options_text += f"Option {i+1}: {option.description}\n"
                    if option.is_correct:
                        correct_option = i+1

                prompt = f"""
                You are an expert educational assistant. Explain the following multiple-choice question and why the correct answer is the best choice.

                QUESTION: {part_data.description}

                OPTIONS:
                {options_text}

                CORRECT ANSWER: Option {correct_option}

                CONTEXT (use this to enhance your explanation):
                {context_text}

                Provide a VERY CONCISE explanation (maximum 150 words) of why this is the correct answer. Focus on the key concepts and principles. Use the context above to provide more detailed and accurate explanations.

                IMPORTANT FORMATTING RULES:
                - Use ONLY plain text with headings marked by # symbols
                - DO NOT use markdown formatting like **bold**, *italic*, or `code`
                - DO NOT use markdown lists with - or * symbols
                - Use numbered lists (1., 2., 3.) or bullet points with • symbol only
                - Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math
                - Keep text clean and simple for web display

                Format your response using this structure:
                # Key Concept
                Brief explanation of the main concept being tested

                # Why Option {correct_option} is Correct
                Concise explanation with clear reasoning

                """
            else:
                # For text/SAQ questions
                prompt = f"""
                You are an expert educational assistant. Explain the following question and its answer.

                QUESTION: {part_data.description}

                MODEL ANSWER: {part_data.answer}

                CONTEXT (use this to enhance your explanation):
                {context_text}

                Provide a VERY CONCISE explanation (maximum 150 words) focusing only on the key concepts and reasoning. Use the context above to provide more detailed and accurate explanations.

                IMPORTANT FORMATTING RULES:
                - Use ONLY plain text with headings marked by # symbols
                - DO NOT use markdown formatting like **bold**, *italic*, or `code`
                - DO NOT use markdown lists with - or * symbols
                - Use numbered lists (1., 2., 3.) or bullet points with • symbol only
                - Use LaTeX for mathematical expressions: $...$ for inline and $$...$$ for display math
                - Keep text clean and simple for web display

                Format your response using this structure:
                # Key Concept
                Brief explanation of the main concept being tested (1-2 sentences)

                # Solution Approach
                Concise explanation of the approach to solve this problem (2-3 sentences)

                # Critical Points
                • Point 1
                • Point 2

                """

            # Create a streaming response
            def generate():
                generation_config = {
                    "temperature": 0.3,  # Slightly creative but mostly factual
                    "top_p": 0.95,
                    "top_k": 40,
                    "max_output_tokens": 4096,
                }

                safety_settings = [
                    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE"},
                    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE"}
                ]

                response = gemini_grading_client.generate_content(
                    prompt,
                    generation_config=generation_config,
                    safety_settings=safety_settings,
                    stream=True
                )

                for chunk in response:
                    if chunk.text:
                        yield chunk.text

            app_logger.info(f"Answer explanation requested - User ID: {session['user_id']}, Question ID: {question_id}, Part ID: {part_id}")
            return app.response_class(generate(), mimetype='text/plain')

        except Exception as e:
            error_logger.exception(f"Error generating answer explanation: {str(e)}")
            return jsonify({
                'status': 'error',
                'message': 'An error occurred while generating the explanation'
            }), 500

    @app.route("/get_activity_data/<int:user_id>")
    @login_required # Or admin_required? Check permissions needed.
    def get_activity_data(user_id):
        """Retrieves daily activity counts for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        # e.g., is the user themselves or an admin.
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query activities for the specified user
            activities = DailyActivity.query.filter_by(user_id=user_id).all()
            # Format data as date string -> count dictionary
            activity_data = {activity.date.isoformat(): activity.activity_count for activity in activities}

            return jsonify(activity_data)
        except Exception as e:
             error_logger.exception(f"Error fetching activity data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve activity data.'}), 500

    @app.route("/get_daily_time_data/<int:user_id>")
    @login_required
    def get_daily_time_data(user_id):
        """Retrieves daily active time data for a user."""
        # Security check: Ensure the requesting user has permission to view this data
        requesting_user_id = session['user_id']
        requesting_user = User.query.get(requesting_user_id)
        if user_id != requesting_user_id and (not requesting_user or requesting_user.role != 'admin'):
             return jsonify({'status': 'error', 'message': 'Permission denied.'}), 403

        try:
            # Query daily active time for the specified user
            daily_times = DailyActiveTime.query.filter_by(user_id=user_id).all()
            # Format data as date string -> seconds dictionary
            time_data = {time_entry.date.isoformat(): time_entry.active_time for time_entry in daily_times}

            return jsonify(time_data)
        except Exception as e:
             error_logger.exception(f"Error fetching daily time data for user {user_id}: {e}")
             return jsonify({'status': 'error', 'message': 'Could not retrieve daily time data.'}), 500

    @app.route("/update_active_time", methods=['POST'])
    @login_required
    def update_active_time():
        """Updates the active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'seconds' not in data:
            return jsonify({'status': 'error', 'message': 'Missing seconds parameter'}), 400

        try:
            seconds = int(data['seconds'])
            if seconds <= 0:
                return jsonify({'status': 'error', 'message': 'Seconds must be positive'}), 400

            # Get today's date
            today = datetime.now().date()

            # Get or create the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            if not daily_active_time:
                daily_active_time = DailyActiveTime(
                    user_id=user_id,
                    date=today,
                    active_time=0
                )
                db.session.add(daily_active_time)

            # Update the active time
            daily_active_time.active_time += seconds

            # Update the user's last_active timestamp
            user = User.query.get(user_id)
            if user:
                user.last_active = datetime.now()

            db.session.commit()

            # Trigger feed generation for the user (but only occasionally to avoid spam)
            # Only trigger if the user has accumulated significant time (every 30 minutes)
            if daily_active_time.active_time % 1800 < seconds:  # Every 30 minutes
                try:
                    from routes.feed_utils import trigger_feed_generation_for_user
                    trigger_feed_generation_for_user(user_id)
                except Exception as e:
                    app_logger.warning(f"Failed to generate feed posts for user {user_id}: {e}")

            # Return the updated active time
            return jsonify({
                'status': 'success',
                'active_time': daily_active_time.active_time
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid seconds value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error updating active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not update active time'}), 500

    @app.route("/get_active_time", methods=['GET'])
    @login_required
    def get_active_time():
        """Gets the total active time for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']

        try:
            # Get today's date
            today = datetime.now().date()

            # Get the DailyActiveTime record for today
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Get total active time across all days
            from sqlalchemy import func
            total_time_result = db.session.query(func.sum(DailyActiveTime.active_time)).filter(
                DailyActiveTime.user_id == user_id
            ).first()

            total_time = total_time_result[0] if total_time_result[0] else 0

            # Get the user's daily time goal
            user = User.query.get(user_id)
            daily_goal = user.daily_time_goal if user and user.daily_time_goal else 3600  # Default: 1 hour

            # Calculate progress percentage
            progress_percent = min(round((today_time / daily_goal) * 100), 100) if daily_goal > 0 else 0

            return jsonify({
                'status': 'success',
                'today_time': today_time,
                'total_time': total_time,
                'daily_goal': daily_goal,
                'progress_percent': progress_percent
            })

        except Exception as e:
            error_logger.exception(f"Error getting active time for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not retrieve active time'}), 500

    @app.route("/set_time_goal", methods=['POST'])
    @login_required
    def set_time_goal():
        """Sets the daily time goal for the current user."""
        if 'user_id' not in session:
            return jsonify({'status': 'error', 'message': 'Not logged in'}), 401

        user_id = session['user_id']
        data = request.get_json()

        if not data or 'goal_minutes' not in data:
            return jsonify({'status': 'error', 'message': 'Missing goal_minutes parameter'}), 400

        try:
            goal_minutes = int(data['goal_minutes'])
            if goal_minutes <= 0:
                return jsonify({'status': 'error', 'message': 'Goal minutes must be positive'}), 400

            # Convert minutes to seconds
            goal_seconds = goal_minutes * 60

            # Update the user's daily time goal
            user = User.query.get(user_id)
            if not user:
                return jsonify({'status': 'error', 'message': 'User not found'}), 404

            user.daily_time_goal = goal_seconds
            db.session.commit()

            # Get today's active time for progress calculation
            today = datetime.now().date()
            daily_active_time = DailyActiveTime.query.filter_by(
                user_id=user_id,
                date=today
            ).first()

            today_time = daily_active_time.active_time if daily_active_time else 0

            # Calculate progress percentage
            progress_percent = min(round((today_time / goal_seconds) * 100), 100) if goal_seconds > 0 else 0

            return jsonify({
                'status': 'success',
                'daily_goal': goal_seconds,
                'goal_minutes': goal_minutes,
                'progress_percent': progress_percent
            })

        except ValueError:
            return jsonify({'status': 'error', 'message': 'Invalid goal_minutes value'}), 400
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error setting time goal for user {user_id}: {e}")
            return jsonify({'status': 'error', 'message': 'Could not set time goal'}), 500
