#!/usr/bin/env python3
"""
Semantic search functionality for chemistry notes using Pinecone
"""

import os
import sys
from typing import List, Dict, Optional
from dataclasses import dataclass
from pinecone import Pinecone

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

NAMESPACE = "hierarchical"

# Initialize Pinecone
pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))

@dataclass
class SearchResult:
    """Represents a search result from Pinecone"""
    id: str
    score: float
    heading_content: str
    anchor_id: str
    learning_outcome: str
    chapter: str
    chapter_slug: str
    url: str
    document_name: str
    full_reference: str
    heading_level: int
    page_number: int

def semantic_search(query: str, top_k: int = 10, score_threshold: float = 0.3) -> List[SearchResult]:
    """
    Perform semantic search on chemistry notes
    
    Args:
        query: Search query text
        top_k: Number of results to return
        score_threshold: Minimum similarity score threshold
    
    Returns:
        List of SearchResult objects
    """
    try:
        print(f"🔍 Searching for: '{query}'")
        
        # Query Pinecone using the correct search format
        query_payload = {
            "inputs": {
                "text": query
            },
            "top_k": top_k
        }

        response = index.search(
            namespace=NAMESPACE,
            query=query_payload
        )

        # Debug: print response structure
        print(f"🔍 Response type: {type(response)}")
        print(f"🔍 Response: {response}")

        if hasattr(response, '__dict__'):
            print(f"🔍 Response attributes: {list(response.__dict__.keys())}")

        if isinstance(response, dict):
            print(f"🔍 Response keys: {list(response.keys())}")
            if 'result' in response:
                print(f"🔍 Result keys: {list(response['result'].keys())}")
                if 'hits' in response['result']:
                    print(f"🔍 Found {len(response['result']['hits'])} hits")

        results = []

        # Parse the response in the correct format
        # Convert response to dict if it's a Pinecone response object
        if hasattr(response, 'to_dict'):
            response_dict = response.to_dict()
        elif hasattr(response, '__dict__'):
            response_dict = dict(response)
        else:
            response_dict = response

        if 'result' in response_dict and 'hits' in response_dict['result']:
            for item in response_dict['result']['hits']:
                # Filter by score threshold
                score = item.get('_score', 0)
                if score < score_threshold:
                    continue

                fields = item.get('fields', {})

                result = SearchResult(
                    id=item.get('_id', ''),
                    score=score,
                    heading_content=fields.get('heading_content', ''),
                    anchor_id=fields.get('anchor_id', ''),
                    learning_outcome=fields.get('learning_outcome', ''),
                    chapter=fields.get('chapter', ''),
                    chapter_slug=fields.get('chapter_slug', ''),
                    url=fields.get('url', ''),
                    document_name=fields.get('document_name', ''),
                    full_reference=fields.get('full_reference', ''),
                    heading_level=int(fields.get('heading_level', 0)),
                    page_number=int(fields.get('page_number', 0))
                )

                results.append(result)
        
        print(f"✅ Found {len(results)} relevant results")
        return results
        
    except Exception as e:
        print(f"❌ Error during search: {e}")
        return []

def search_and_get_anchor_urls(query: str, top_k: int = 5) -> List[str]:
    """
    Search and return just the anchor URLs for easy integration
    
    Args:
        query: Search query text
        top_k: Number of results to return
    
    Returns:
        List of anchor URLs
    """
    results = semantic_search(query, top_k)
    return [result.url for result in results]

def search_with_context(query: str, top_k: int = 5) -> List[Dict]:
    """
    Search and return results with full context for display
    
    Args:
        query: Search query text
        top_k: Number of results to return
    
    Returns:
        List of dictionaries with search result data
    """
    results = semantic_search(query, top_k)
    
    return [
        {
            'title': result.heading_content,
            'chapter': result.chapter,
            'url': result.url,
            'anchor_id': result.anchor_id,
            'learning_outcome': result.learning_outcome,
            'score': round(result.score, 3),
            'full_reference': result.full_reference
        }
        for result in results
    ]

def test_search_functionality():
    """Test the search functionality with sample queries"""
    test_queries = [
        "chemical bonding",
        "acid base equilibrium",
        "organic reactions",
        "periodic trends",
        "reaction kinetics",
        "molecular structure"
    ]
    
    print("🧪 Testing Semantic Search Functionality")
    print("=" * 50)
    
    for query in test_queries:
        print(f"\n🔍 Query: '{query}'")
        results = search_with_context(query, top_k=3)
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"   {i}. {result['title']} (Score: {result['score']})")
                print(f"      Chapter: {result['chapter']}")
                print(f"      URL: {result['url']}")
                print()
        else:
            print("   No results found")
        
        print("-" * 30)

def search_interactive():
    """Interactive search mode for testing"""
    print("🔍 Interactive Chemistry Notes Search")
    print("Type 'quit' to exit")
    print("=" * 40)
    
    while True:
        query = input("\nEnter search query: ").strip()
        
        if query.lower() in ['quit', 'exit', 'q']:
            break
        
        if not query:
            continue
        
        results = search_with_context(query, top_k=5)
        
        if results:
            print(f"\n📚 Found {len(results)} results:")
            for i, result in enumerate(results, 1):
                print(f"\n{i}. {result['title']}")
                print(f"   Chapter: {result['chapter']}")
                print(f"   Score: {result['score']}")
                print(f"   URL: {result['url']}")
                print(f"   Learning Outcome: {result['learning_outcome'][:100]}...")
        else:
            print("\n❌ No results found. Try a different query.")

# Flask integration functions
def get_search_results_for_api(query: str, limit: int = 10) -> Dict:
    """
    Get search results formatted for API response
    
    Args:
        query: Search query
        limit: Maximum number of results
    
    Returns:
        Dictionary with search results and metadata
    """
    results = search_with_context(query, top_k=limit)
    
    return {
        'query': query,
        'total_results': len(results),
        'results': results,
        'success': True
    }

def search_by_topic(topic: str, top_k: int = 5) -> List[Dict]:
    """
    Search for content related to a specific chemistry topic
    
    Args:
        topic: Chemistry topic (e.g., 'bonding', 'kinetics', 'equilibrium')
        top_k: Number of results to return
    
    Returns:
        List of search results
    """
    # Enhanced topic queries
    topic_queries = {
        'bonding': 'chemical bonds ionic covalent metallic intermolecular forces',
        'kinetics': 'reaction rate catalyst activation energy mechanism',
        'equilibrium': 'chemical equilibrium equilibria constant position',
        'acids_bases': 'acid base pH buffer titration indicator',
        'organic': 'organic chemistry alkane alkene alcohol carbonyl',
        'energetics': 'enthalpy entropy energy thermodynamics',
        'electrochemistry': 'electrode cell potential electrolysis redox',
        'periodic': 'periodic table trends ionization electronegativity',
        'structure': 'molecular structure geometry VSEPR shape'
    }
    
    query = topic_queries.get(topic.lower(), topic)
    return search_with_context(query, top_k)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "test":
            test_search_functionality()
        elif sys.argv[1] == "interactive":
            search_interactive()
        else:
            # Single query search
            query = " ".join(sys.argv[1:])
            results = search_with_context(query)
            
            print(f"🔍 Search results for: '{query}'")
            print("=" * 50)
            
            if results:
                for i, result in enumerate(results, 1):
                    print(f"\n{i}. {result['title']}")
                    print(f"   Chapter: {result['chapter']}")
                    print(f"   Score: {result['score']}")
                    print(f"   URL: {result['url']}")
            else:
                print("No results found.")
    else:
        print("Usage:")
        print("  python semantic_search.py test                    # Run test queries")
        print("  python semantic_search.py interactive             # Interactive mode")
        print("  python semantic_search.py 'your search query'     # Single search")
