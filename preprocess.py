from dotenv import load_dotenv
import json
import os
import logging
import google.generativeai as genai
from pinecone import <PERSON>cone
from models import Part, db, Subject, Topic, Question
from app import app
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import time
from threading import Lock


load_dotenv()

class PlatoPreprocess():
    def __init__(self, max_workers=4):
        self.gemini_client = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')
        self.pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
        self.pinecone_index = self.pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))
        self.max_workers = max_workers
        self.db_lock = Lock()  # Thread-safe database operations
        self.processed_count = 0
        self.total_count = 0

    def work(self, part_ids=None, skip_existing=True, subject_id=None):
        """
        Process parts to generate rubrics with parallel processing

        Args:
            part_ids: List of specific part IDs to process. If None, processes all parts.
            skip_existing: If True, skip parts that already have rubrics generated.
            subject_id: Filter by specific subject ID. If None, processes all subjects.
        """
        with app.app_context():
            # Get parts to process - only SAQ (short answer questions)
            if part_ids:
                parts_query = Part.query.filter(
                    Part.id.in_(part_ids),
                    Part.input_type == 'saq'  # Only short answer questions
                )
            else:
                parts_query = Part.query.filter(Part.input_type == 'saq')

            # Filter by subject if specified
            if subject_id:
                parts_query = parts_query.join(Question).join(Topic).join(Subject).filter(
                    Subject.id == subject_id
                )

            if skip_existing:
                parts_query = parts_query.filter(Part.content_rubric.is_(None))

            parts = parts_query.all()
            self.total_count = len(parts)

            if not parts:
                print("No parts to process.")
                return

            print(f"Processing {self.total_count} parts with {self.max_workers} workers...")
            start_time = time.time()

            # Process parts in parallel
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tasks
                future_to_part = {
                    executor.submit(self.process_single_part, part): part
                    for part in parts
                }

                # Process completed tasks
                for future in as_completed(future_to_part):
                    part = future_to_part[future]
                    try:
                        result = future.result()
                        if result['success']:
                            self.processed_count += 1
                            print(f"✓ Processed part {part.id} ({self.processed_count}/{self.total_count})")
                        else:
                            print(f"✗ Failed to process part {part.id}: {result['error']}")
                    except Exception as e:
                        print(f"✗ Exception processing part {part.id}: {str(e)}")

            end_time = time.time()
            print(f"\nCompleted processing {self.processed_count}/{self.total_count} parts in {end_time - start_time:.2f} seconds")

    def process_single_part(self, part):
        """Process a single part to generate and save rubrics"""
        try:
            # Generate rubrics
            content_rubrics = self.generate_content_requirements(part)
            if not content_rubrics:
                return {'success': False, 'error': 'Failed to generate content rubrics'}

            scoring_rubrics = self.generate_scoring_rubrics(content_rubrics, part)
            if not scoring_rubrics:
                return {'success': False, 'error': 'Failed to generate scoring rubrics'}

            # Save to database
            success = self.save_rubrics_to_db(part.id, content_rubrics, scoring_rubrics)
            if not success:
                return {'success': False, 'error': 'Failed to save rubrics to database'}

            return {
                'success': True,
                'content_rubrics': content_rubrics,
                'scoring_rubrics': scoring_rubrics
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def save_rubrics_to_db(self, part_id, content_rubrics, scoring_rubrics):
        """Save generated rubrics to the database in a thread-safe manner"""
        try:
            with self.db_lock:
                with app.app_context():
                    part = Part.query.get(part_id)
                    if not part:
                        return False

                    # Convert dictionaries to JSON strings
                    part.content_rubric = json.dumps(content_rubrics)
                    part.scoring_rubric = json.dumps(scoring_rubrics)
                    part.rubrics_generated_at = datetime.now()

                    db.session.commit()
                    return True

        except Exception as e:
            logging.error(f"Failed to save rubrics for part {part_id}: {str(e)}")
            try:
                db.session.rollback()
            except:
                pass
            return False

    def get_rubrics_from_db(self, part_id):
        """Retrieve rubrics from database for a given part"""
        try:
            with app.app_context():
                part = Part.query.get(part_id)
                if not part or not part.content_rubric or not part.scoring_rubric:
                    return None

                return {
                    'content_rubric': json.loads(part.content_rubric),
                    'scoring_rubric': json.loads(part.scoring_rubric),
                    'generated_at': part.rubrics_generated_at
                }
        except Exception as e:
            logging.error(f"Failed to retrieve rubrics for part {part_id}: {str(e)}")
            return None

    def list_parts_with_rubrics(self):
        """List all parts that have rubrics generated"""
        try:
            with app.app_context():
                parts = Part.query.filter(
                    Part.content_rubric.isnot(None),
                    Part.scoring_rubric.isnot(None)
                ).all()

                result = []
                for part in parts:
                    result.append({
                        'id': part.id,
                        'description': part.description[:100] + '...' if len(part.description) > 100 else part.description,
                        'score': part.score,
                        'generated_at': part.rubrics_generated_at
                    })
                return result
        except Exception as e:
            logging.error(f"Failed to list parts with rubrics: {str(e)}")
            return []

    def list_available_subjects(self):
        """List all available subjects in the database"""
        try:
            with app.app_context():
                subjects = Subject.query.all()
                result = []
                for subject in subjects:
                    # Count SAQ parts for this subject
                    saq_count = Part.query.join(Question).join(Topic).join(Subject).filter(
                        Subject.id == subject.id,
                        Part.input_type == 'saq'
                    ).count()

                    # Count SAQ parts with rubrics
                    saq_with_rubrics = Part.query.join(Question).join(Topic).join(Subject).filter(
                        Subject.id == subject.id,
                        Part.input_type == 'saq',
                        Part.content_rubric.isnot(None)
                    ).count()

                    result.append({
                        'id': subject.id,
                        'name': subject.name,
                        'syllabus': subject.syllabus,
                        'saq_parts': saq_count,
                        'saq_with_rubrics': saq_with_rubrics,
                        'completion_rate': (saq_with_rubrics / saq_count * 100) if saq_count > 0 else 0
                    })
                return result
        except Exception as e:
            logging.error(f"Failed to list subjects: {str(e)}")
            return []

    def generate_content_requirements(self, part: Part) -> dict:
        """Generate concise content requirements for grading responses"""
        context_text = ""

        # Ensure we're in app context for database queries
        with app.app_context():
            # Get context from Pinecone (reusing existing query logic)
            query_text = f"{part.description}"
            if part.answer:
                query_text += f" {part.answer}"
            if part.question.topic:
                query_text += f" {part.question.topic.name}"
            if part.question.topic.subject:
                query_text += f" {part.question.topic.subject.name}"

            query_payload = {
                "inputs": {
                    "text": query_text
                },
                "top_k": 5
            }

            try:
                query_response = self.pinecone_index.search(
                    namespace="__default__",
                    query=query_payload
                )
            except Exception as e_pinecone:
                logging.warning(f"Pinecone query failed for part {part.id}: {str(e_pinecone)}")

            for item in query_response['result']['hits']:
                context_text += f"{item['fields']['title']}: {item['fields']['content']}\n"

        prompt = f"""
You are an expert educational assessment designer. Create concise content requirements for grading responses to this question.

CONTEXT INFORMATION:
{context_text}

QUESTION:
{part.description}

MODEL ANSWER:
{part.answer or "No model answer provided"}

TOTAL SCORE: {part.score} points

Based on the question, model answer, and context, identify the key content requirements that must be present in a correct response.
For each requirement:
1. Focus on specific concepts, facts, or explanations that must be included
2. Make each requirement clear, objective, and assessable
3. Ensure requirements are distinct from each other
4. Cover all essential elements of the model answer

Return ONLY a valid JSON dictionary where each key is a unique ID and each value is a specific content requirement.
Example: {{"1": "Correctly identifies ionic bonding", "2": "Explains effect of charge on bond strength"}}
"""

        try:
            response = self.gemini_client.generate_content(
                prompt,
                generation_config={
                    'temperature': 0.2,
                    'response_mime_type': 'application/json'
                }
            )
            
            requirements = json.loads(response.text)
            return requirements
        except Exception as e:
            logging.error(f"Failed to generate content requirements for part {part.id}: {str(e)}")
            return {}

    def generate_scoring_rubrics(self, content_rubrics: dict, part: Part) -> dict:
        """Generate scoring rubrics with bit-mapped requirement combinations"""
    
        # Format content requirements for the prompt
        content_requirements_text = ""
        for i, (req_id, requirement) in enumerate(content_rubrics.items()):
            content_requirements_text += f"- Bit {i} ({req_id}): {requirement}\n"
        
        prompt = f"""
You are an expert educational assessment designer. Create a bit-mapped scoring rubric for grading responses.

QUESTION:
{part.description}

MODEL ANSWER:
{part.answer or "No model answer provided"}

TOTAL SCORE: {part.score} points

CONTENT REQUIREMENTS (with bit positions):
{content_requirements_text}

Create a scoring rubric where each requirement is represented by a bit position:
- If requirement at bit 0 is present, toggle bit 0 (value 1)
- If requirement at bit 1 is present, toggle bit 1 (value 2)
- If requirement at bit 2 is present, toggle bit 2 (value 4)
- And so on (bit 3 = 8, bit 4 = 16, etc.)

For example, with 3 requirements (bits 0,1,2):
- All requirements present = bits 0,1,2 = 1+2+4 = 7
- Only requirements at bits 0,1 present = 1+2 = 3
- Only requirement at bit 2 present = 4

Return ONLY a valid JSON dictionary where:
- Each key is an integer representing the bit combination
- Each value is the score that combination earns

Example format for a 3-point question with 3 requirements:
{{
  "7": 3,  // All requirements present (bits 0,1,2)
  "3": 2,  // Requirements at bits 0,1 present, bit 2 missing
  "4": 1,  // Only requirement at bit 2 present
  "0": 0   // No requirements present
}}

Ensure you cover all meaningful combinations and assign appropriate scores from 0 to {part.score}.
If a requirement is important for the overall understanding of a question, all combinations omitting it should be assigned a score of 0.
"""

        try:
            response = self.gemini_client.generate_content(
                prompt,
                generation_config={
                    'temperature': 0.2,
                    'response_mime_type': 'application/json'
                }
            )
            
            # Clean the response text to remove any comments
            response_text = response.text
            response_text = re.sub(r'//.*', '', response_text)  # Remove any comments
            
            scoring_rubrics = json.loads(response_text)
            
            # Convert string keys to integers
            scoring_rubrics = {int(k): v for k, v in scoring_rubrics.items()}
            
            return scoring_rubrics
        except Exception as e:
            logging.error(f"Failed to generate bit-mapped scoring rubrics for part {part.id}: {str(e)}")
            return {}

    def precompute_study_materials(self, part_ids=None, skip_existing=True, subject_id=None):
        """
        Precompute study materials for each requirement of every question part

        Args:
            part_ids: List of specific part IDs to process. If None, processes all parts.
            skip_existing: If True, skip parts that already have study materials precomputed.
            subject_id: Filter by specific subject ID. If None, processes all subjects.
        """
        with app.app_context():
            # Get parts that have rubrics but need study materials
            if part_ids:
                parts_query = Part.query.filter(
                    Part.id.in_(part_ids),
                    Part.input_type == 'saq',
                    Part.content_rubric.isnot(None)
                )
            else:
                parts_query = Part.query.filter(
                    Part.input_type == 'saq',
                    Part.content_rubric.isnot(None)
                )

            # Filter by subject if specified
            if subject_id:
                parts_query = parts_query.join(Question).join(Topic).join(Subject).filter(
                    Subject.id == subject_id
                )

            if skip_existing:
                parts_query = parts_query.filter(Part.study_materials.is_(None))

            parts = parts_query.all()
            self.total_count = len(parts)

            if not parts:
                print("No parts to process for study materials.")
                return

            print(f"Precomputing study materials for {self.total_count} parts with {self.max_workers} workers...")
            start_time = time.time()

            # Process parts in parallel
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit all tasks
                future_to_part = {
                    executor.submit(self.process_study_materials_for_part, part): part
                    for part in parts
                }

                # Process completed tasks
                for future in as_completed(future_to_part):
                    part = future_to_part[future]
                    try:
                        result = future.result()
                        if result['success']:
                            self.processed_count += 1
                            print(f"✓ Processed study materials for part {part.id} ({self.processed_count}/{self.total_count})")
                        else:
                            print(f"✗ Failed to process study materials for part {part.id}: {result['error']}")
                    except Exception as e:
                        print(f"✗ Exception processing study materials for part {part.id}: {str(e)}")

            end_time = time.time()
            print(f"\nCompleted precomputing study materials for {self.processed_count}/{self.total_count} parts in {end_time - start_time:.2f} seconds")

    def process_study_materials_for_part(self, part):
        """Process a single part to precompute study materials for each requirement"""
        try:
            with app.app_context():
                # Parse content rubric
                content_rubric = json.loads(part.content_rubric)
                study_materials = {}

                # For each requirement, query Pinecone for study materials
                for req_id, requirement in content_rubric.items():
                    # Create search query from the requirement and question context
                    search_query = f"{requirement}"
                    if part.description:
                        search_query += f" {part.description}"
                    if part.answer:
                        search_query += f" {part.answer}"
                    if part.question.topic:
                        search_query += f" {part.question.topic.name}"
                    if part.question.topic and part.question.topic.subject:
                        search_query += f" {part.question.topic.subject.name}"

                    # Search for relevant materials using the existing search function
                    try:
                        search_results = self.search_pinecone_for_materials(search_query, top_k=3)

                        if search_results:
                            study_materials[req_id] = {
                                'requirement': requirement,
                                'materials': search_results
                            }
                            print(f"  Found {len(search_results)} study materials for requirement {req_id}")
                        else:
                            print(f"  No study materials found for requirement {req_id}")
                            study_materials[req_id] = {
                                'requirement': requirement,
                                'materials': []
                            }

                    except Exception as e:
                        print(f"  Error searching for requirement {req_id}: {str(e)}")
                        study_materials[req_id] = {
                            'requirement': requirement,
                            'materials': []
                        }

                # Save study materials to database
                success = self.save_study_materials_to_db(part.id, study_materials)
                if not success:
                    return {'success': False, 'error': 'Failed to save study materials to database'}

                return {
                    'success': True,
                    'study_materials': study_materials
                }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def search_pinecone_for_materials(self, search_query, top_k=3):
        """Search Pinecone for study materials and return formatted results"""
        try:
            query_payload = {
                "inputs": {
                    "text": search_query
                },
                "top_k": top_k
            }

            print(f"🔍 Searching for: '{search_query}'")

            query_response = self.pinecone_index.search(
                namespace="__default__",
                query=query_payload
            )

            results = []
            for item in query_response['result']['hits']:
                # Extract metadata
                fields = item.get('fields', {})

                # Debug: Print available fields for the first result
                if len(results) == 0:
                    print(f"Available fields in Pinecone: {list(fields.keys())}")

                # Extract fields using the correct field names from the index
                title = fields.get('heading_content') or fields.get('full_reference') or 'Unknown Title'
                chapter = fields.get('chapter') or fields.get('document_name') or 'Unknown Chapter'

                # Use the URL directly from Pinecone (it's already properly formatted)
                url = fields.get('url', '#')
                anchor_id = fields.get('anchor_id', '')

                result = {
                    'title': title,
                    'chapter': chapter,
                    'url': url,
                    'anchor_id': anchor_id,
                    'learning_outcome': fields.get('learning_outcome', ''),
                    'score': item.get('score', 0.0),
                    'full_reference': fields.get('full_reference', ''),
                    'chapter_slug': fields.get('chapter_slug', ''),
                    'page_number': fields.get('page_number', ''),
                    'content': fields.get('content', '')
                }
                results.append(result)

            print(f"✅ Found {len(results)} relevant results")
            return results

        except Exception as e:
            print(f"❌ Error searching Pinecone: {str(e)}")
            return []

    def save_study_materials_to_db(self, part_id, study_materials):
        """Save precomputed study materials to the database in a thread-safe manner"""
        try:
            with self.db_lock:
                with app.app_context():
                    part = Part.query.get(part_id)
                    if not part:
                        return False

                    # Convert dictionary to JSON string
                    part.study_materials = json.dumps(study_materials)
                    part.study_materials_generated_at = datetime.now()

                    db.session.commit()
                    return True

        except Exception as e:
            logging.error(f"Failed to save study materials for part {part_id}: {str(e)}")
            try:
                db.session.rollback()
            except:
                pass
            return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Generate rubrics for short answer question parts')
    parser.add_argument('--workers', type=int, default=4, help='Number of parallel workers (default: 4)')
    parser.add_argument('--part-ids', nargs='+', type=int, help='Specific part IDs to process')
    parser.add_argument('--no-skip-existing', action='store_true', help='Process parts even if they already have rubrics')
    parser.add_argument('--test', action='store_true', help='Test mode: process only first SAQ part')
    parser.add_argument('--subject-id', type=int, help='Filter by subject ID (use --list-subjects to see available subjects)')
    parser.add_argument('--list-subjects', action='store_true', help='List available subjects and their SAQ statistics')
    parser.add_argument('--study-materials', action='store_true', help='Precompute study materials for each requirement (requires existing rubrics)')
    parser.add_argument('--study-materials-only', action='store_true', help='Only precompute study materials, skip rubric generation')

    args = parser.parse_args()

    # Initialize preprocessor
    plato = PlatoPreprocess(max_workers=args.workers)

    # Handle list subjects command
    if args.list_subjects:
        subjects = plato.list_available_subjects()
        if subjects:
            print("Available Subjects (Short Answer Questions only):")
            print("=" * 70)
            print(f"{'ID':<3} {'Name':<20} {'Syllabus':<8} {'SAQ Parts':<10} {'With Rubrics':<12} {'Complete %':<10}")
            print("-" * 70)
            for subject in subjects:
                print(f"{subject['id']:<3} {subject['name']:<20} {subject['syllabus']:<8} {subject['saq_parts']:<10} {subject['saq_with_rubrics']:<12} {subject['completion_rate']:<10.1f}")
        else:
            print("No subjects found")
        exit(0)

    # Determine which parts to process
    part_ids = args.part_ids
    skip_existing = not args.no_skip_existing
    subject_id = args.subject_id

    if args.test:
        # Test mode: process only the first available SAQ part
        with app.app_context():
            query = Part.query.filter(Part.input_type == 'saq')
            if subject_id:
                query = query.join(Question).join(Topic).join(Subject).filter(Subject.id == subject_id)
            first_part = query.first()

            if first_part:
                part_ids = [first_part.id]
                skip_existing = False
                print(f"Test mode: processing SAQ part {first_part.id}")
            else:
                subject_msg = f" for subject {subject_id}" if subject_id else ""
                print(f"No SAQ parts found for testing{subject_msg}")
                exit(1)

    # Run the processing
    if args.study_materials_only:
        # Only precompute study materials
        plato.precompute_study_materials(part_ids=part_ids, skip_existing=skip_existing, subject_id=subject_id)
    elif args.study_materials:
        # Generate rubrics first, then precompute study materials
        plato.work(part_ids=part_ids, skip_existing=skip_existing, subject_id=subject_id)
        plato.precompute_study_materials(part_ids=part_ids, skip_existing=skip_existing, subject_id=subject_id)
    else:
        # Only generate rubrics (default behavior)
        plato.work(part_ids=part_ids, skip_existing=skip_existing, subject_id=subject_id)